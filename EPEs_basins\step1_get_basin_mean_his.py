import os
import numpy as np
import rasterio
from pathlib import Path
import h5py
import pandas as pd
import concurrent.futures
from scipy.io import loadmat


def process_year(year, mask_data, basin_ids, mat_dir, output_dir):
    """
    处理单个年份的数据
    """
    print(f"\n=== 处理{year}年数据 ===")
    
    # 读取mat文件
    mat_path = os.path.join(mat_dir, f"Pre_{year}.mat")
    
    try:
        # 尝试使用scipy.io.loadmat读取
        mat_data = loadmat(mat_path)
        data_all = mat_data['Pre_year']
    except:
        # 如果失败，尝试使用h5py读取
        with h5py.File(mat_path, 'r') as mat_file:
            data_all = np.array(mat_file['Pre_year'])
            data_all = data_all.T
    
    # 确保数据形状正确 (pixels, days)
    if data_all.shape[1] != 365:
        data_all = data_all.T
    
    # 计算每个流域的逐日平均值
    basin_daily_means = np.zeros((len(basin_ids), 365))
    
    for b, basin_id in enumerate(basin_ids):
        # 获取当前流域的所有网格点
        basin_pixels = mask_data == basin_id
        
        if np.any(basin_pixels):
            # 计算该流域每天的均值
            for day in range(365):
                basin_data = data_all[basin_pixels, day]
                basin_daily_means[b, day] = np.nanmean(basin_data)
        else:
            basin_daily_means[b, :] = np.nan
    
    # 准备CSV数据
    # 创建表头
    headers = ['BasinID'] + [f'Day_{day+1:03d}' for day in range(365)]
    
    # 创建数据矩阵
    csv_data = np.zeros((len(basin_ids), 366))  # 366列：1列BasinID + 365列数据
    csv_data[:, 0] = basin_ids  # 第一列是流域ID
    csv_data[:, 1:] = basin_daily_means  # 其余列是每日数据
    
    # 转换为DataFrame
    df = pd.DataFrame(csv_data, columns=headers)
    df['BasinID'] = df['BasinID'].astype(int)
    
    # 保存为CSV文件
    os.makedirs(output_dir, exist_ok=True)
    csv_filename = os.path.join(output_dir, f'basin_daily_means_{year}.csv')
    df.to_csv(csv_filename, index=False)
    
    print(f'已完成{year}年数据处理，保存到: {csv_filename}')
    return year


def main():
    """
    主函数
    """
    # 路径定义
    geo_filename = '/mnt/sdb2/yuanshuai/wanpan/yuan/clip_biaozhun_tif/clip_tif_global_600_1440.tif'
    mask_geo_filename = '/mnt/sdb2/yuanshuai/wanpan/yuan/paper3_new03/shp/basin_Country_res/basin_id_raster.tif'
    mat_dir = '/mnt/sdb2/yuanshuai/wanpan/yuan/ERA5-Land/fbl_025/mat/Pre/day'
    output_dir = '/mnt/sdb2/yuanshuai/wanpan/yuan/paper3/pre_data/historical/basins_daily_mean'
    
    # 读取地理参考信息
    with rasterio.open(geo_filename) as src:
        profile = src.profile
        m, n = src.shape
    
    # 读入掩膜文件
    with rasterio.open(mask_geo_filename) as src:
        mask_data = src.read(1)
        mask_data = mask_data.reshape(m * n)
    
    # 获取所有唯一的流域ID(排除0和NaN)
    basin_ids = np.unique(mask_data)
    basin_ids = basin_ids[basin_ids > 0]
    basin_ids = basin_ids[~np.isnan(basin_ids)]
    
    print(f"找到 {len(basin_ids)} 个流域")
    print(f"流域ID范围: {basin_ids.min()} - {basin_ids.max()}")
    
    # 定义年份范围
    years = list(range(1971, 2021))
    
    # 使用多线程处理
    max_workers = min(10, len(years))  # 限制最大线程数
    print(f"使用 {max_workers} 个线程处理数据")
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        futures = [
            executor.submit(process_year, year, mask_data, basin_ids, mat_dir, output_dir)
            for year in years
        ]
        
        # 等待所有任务完成
        completed_years = []
        for future in concurrent.futures.as_completed(futures):
            try:
                year = future.result()
                completed_years.append(year)
                print(f"进度: {len(completed_years)}/{len(years)} 年完成")
            except Exception as e:
                print(f"处理年份时出错: {e}")
    
    print(f"\n所有数据处理完成！共处理了 {len(completed_years)} 年的数据")
    print(f"输出目录: {output_dir}")


if __name__ == '__main__':
    main()
